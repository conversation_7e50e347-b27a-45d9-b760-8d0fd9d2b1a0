'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import { useWorkspace } from '../context/WorkspaceContext';
import { DragDropProvider } from '../context/DragDropContext';
import { LayoutNode } from '../types';
import { Panel } from './Panel';
import { TabGroup } from './TabGroup';

interface WorkspaceLayoutProps {
  className?: string;
  layoutId?: string;
}

export function WorkspaceLayout({ className, layoutId }: WorkspaceLayoutProps) {
  const { activeLayout, state } = useWorkspace();
  
  const layout = layoutId 
    ? state.layouts.find(l => l.id === layoutId) 
    : activeLayout;

  if (!layout) {
    return (
      <div className={cn("flex items-center justify-center h-full w-full", className)}>
        <div className="text-center">
          <h2 className="text-lg font-semibold mb-2">No Workspace Layout</h2>
          <p className="text-muted-foreground">Create a new layout to get started</p>
        </div>
      </div>
    );
  }

  return (
    <DragDropProvider>
      <div className={cn("h-full w-full bg-background", className)}>
        <LayoutRenderer node={layout.layout} layout={layout} />
      </div>
    </DragDropProvider>
  );
}

interface LayoutRendererProps {
  node: LayoutNode;
  layout: any;
}

function LayoutRenderer({ node, layout }: LayoutRendererProps) {
  if (node.type === 'split' && node.children) {
    return (
      <ResizablePanelGroup
        direction={node.direction || 'horizontal'}
        className="h-full w-full"
      >
        {node.children.map((child, index) => (
          <React.Fragment key={child.id}>
            <ResizablePanel
              defaultSize={child.size || 50}
              minSize={child.minSize || 10}
              maxSize={child.maxSize || 90}
            >
              <LayoutRenderer node={child} layout={layout} />
            </ResizablePanel>
            {index < node.children.length - 1 && (
              <ResizableHandle withHandle />
            )}
          </React.Fragment>
        ))}
      </ResizablePanelGroup>
    );
  }

  if (node.type === 'panel') {
    const panel = layout.panels.find((p: any) => p.id === node.id);
    if (!panel) return null;
    
    return <Panel config={panel} />;
  }

  if (node.type === 'tabGroup') {
    const tabGroup = layout.tabGroups.find((tg: any) => tg.id === node.id);
    if (!tabGroup) return null;
    
    return <TabGroup config={tabGroup} layoutId={layout.id} />;
  }

  return null;
}

// Default layout configurations
export const defaultLayouts = {
  codeEditor: {
    name: 'Code Editor',
    panels: [
      {
        id: 'explorer',
        title: 'Explorer',
        type: 'explorer' as const,
        closable: false,
        minSize: 15,
        maxSize: 40,
        defaultSize: 20,
      },
      {
        id: 'terminal',
        title: 'Terminal',
        type: 'terminal' as const,
        closable: true,
        minSize: 10,
        maxSize: 50,
        defaultSize: 25,
      },
    ],
    tabGroups: [
      {
        id: 'main-editor',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: true,
      },
    ],
    layout: {
      type: 'split' as const,
      id: 'root',
      direction: 'horizontal' as const,
      children: [
        {
          type: 'panel' as const,
          id: 'explorer',
          size: 20,
        },
        {
          type: 'split' as const,
          id: 'main-area',
          direction: 'vertical' as const,
          size: 80,
          children: [
            {
              type: 'tabGroup' as const,
              id: 'main-editor',
              size: 75,
            },
            {
              type: 'panel' as const,
              id: 'terminal',
              size: 25,
            },
          ],
        },
      ],
    },
  },

  dashboard: {
    name: 'Dashboard',
    panels: [
      {
        id: 'sidebar',
        title: 'Navigation',
        type: 'explorer' as const,
        closable: false,
        minSize: 15,
        maxSize: 30,
        defaultSize: 20,
      },
    ],
    tabGroups: [
      {
        id: 'main-content',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: false,
      },
    ],
    layout: {
      type: 'split' as const,
      id: 'root',
      direction: 'horizontal' as const,
      children: [
        {
          type: 'panel' as const,
          id: 'sidebar',
          size: 20,
        },
        {
          type: 'tabGroup' as const,
          id: 'main-content',
          size: 80,
        },
      ],
    },
  },

  aiWorkspace: {
    name: 'AI Workspace',
    panels: [
      {
        id: 'ai-chat',
        title: 'AI Assistant',
        type: 'ai-chat' as const,
        closable: false,
        minSize: 20,
        maxSize: 50,
        defaultSize: 30,
      },
      {
        id: 'terminal',
        title: 'Terminal',
        type: 'terminal' as const,
        closable: true,
        minSize: 15,
        maxSize: 40,
        defaultSize: 25,
      },
    ],
    tabGroups: [
      {
        id: 'editor-group',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: true,
      },
    ],
    layout: {
      type: 'split' as const,
      id: 'root',
      direction: 'horizontal' as const,
      children: [
        {
          type: 'panel' as const,
          id: 'ai-chat',
          size: 30,
        },
        {
          type: 'split' as const,
          id: 'main-area',
          direction: 'vertical' as const,
          size: 70,
          children: [
            {
              type: 'tabGroup' as const,
              id: 'editor-group',
              size: 70,
            },
            {
              type: 'panel' as const,
              id: 'terminal',
              size: 30,
            },
          ],
        },
      ],
    },
  },
};
