'use client';

import React, { createContext, useContext, useState, useCallback, useRef } from 'react';
import { DragDropContextType, DropZone } from '../types';

const DragDropContext = createContext<DragDropContextType | null>(null);

export function DragDropProvider({ children }: { children: React.ReactNode }) {
  const [draggedItem, setDraggedItem] = useState<DragDropContextType['draggedItem']>(null);
  const dropZonesRef = useRef<Map<string, DropZone>>(new Map());

  const registerDropZone = useCallback((zone: DropZone) => {
    dropZonesRef.current.set(zone.id, zone);
  }, []);

  const unregisterDropZone = useCallback((zoneId: string) => {
    dropZonesRef.current.delete(zoneId);
  }, []);

  const contextValue: DragDropContextType = {
    draggedItem,
    setDraggedItem,
    dropZones: Array.from(dropZonesRef.current.values()),
    registerDropZone,
    unregisterDropZone,
  };

  return (
    <DragDropContext.Provider value={contextValue}>
      {children}
    </DragDropContext.Provider>
  );
}

export function useDragDrop() {
  const context = useContext(DragDropContext);
  if (!context) {
    throw new Error('useDragDrop must be used within a DragDropProvider');
  }
  return context;
}
