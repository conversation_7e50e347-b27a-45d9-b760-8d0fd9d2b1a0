'use client';

import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import { 
  WorkspaceContextType, 
  WorkspaceState, 
  WorkspaceLayoutConfig, 
  PanelConfig, 
  TabConfig, 
  TabGroupConfig,
  WorkspacePreferences 
} from '../types';

// Initial state
const initialState: WorkspaceState = {
  layouts: [],
  activeLayoutId: '',
  preferences: {
    autoSave: true,
    tabSize: 2,
    wordWrap: true,
    minimap: true,
    lineNumbers: true,
    theme: 'dark',
    fontSize: 14,
    fontFamily: 'JetBrains Mono, monospace',
  },
};

// Action types
type WorkspaceAction =
  | { type: 'CREATE_LAYOUT'; payload: WorkspaceLayoutConfig }
  | { type: 'UPDATE_LAYOUT'; payload: { id: string; updates: Partial<WorkspaceLayoutConfig> } }
  | { type: 'DELETE_LAYOUT'; payload: string }
  | { type: 'SET_ACTIVE_LAYOUT'; payload: string }
  | { type: 'ADD_PANEL'; payload: { layoutId: string; panel: PanelConfig; position?: string } }
  | { type: 'REMOVE_PANEL'; payload: { layoutId: string; panelId: string } }
  | { type: 'UPDATE_PANEL'; payload: { layoutId: string; panelId: string; updates: Partial<PanelConfig> } }
  | { type: 'ADD_TAB'; payload: { layoutId: string; groupId: string; tab: TabConfig; index?: number } }
  | { type: 'REMOVE_TAB'; payload: { layoutId: string; groupId: string; tabId: string } }
  | { type: 'UPDATE_TAB'; payload: { layoutId: string; groupId: string; tabId: string; updates: Partial<TabConfig> } }
  | { type: 'SET_ACTIVE_TAB'; payload: { layoutId: string; groupId: string; tabId: string } }
  | { type: 'MOVE_TAB'; payload: { layoutId: string; fromGroupId: string; toGroupId: string; tabId: string; index?: number } }
  | { type: 'ADD_TAB_GROUP'; payload: { layoutId: string; group: TabGroupConfig; position?: string } }
  | { type: 'REMOVE_TAB_GROUP'; payload: { layoutId: string; groupId: string } }
  | { type: 'SPLIT_TAB_GROUP'; payload: { layoutId: string; groupId: string; direction: 'horizontal' | 'vertical' } }
  | { type: 'UPDATE_PREFERENCES'; payload: Partial<WorkspacePreferences> }
  | { type: 'LOAD_STATE'; payload: WorkspaceState };

// Reducer
function workspaceReducer(state: WorkspaceState, action: WorkspaceAction): WorkspaceState {
  switch (action.type) {
    case 'CREATE_LAYOUT':
      return {
        ...state,
        layouts: [...state.layouts, action.payload],
        activeLayoutId: action.payload.id,
      };

    case 'UPDATE_LAYOUT':
      return {
        ...state,
        layouts: state.layouts.map(layout =>
          layout.id === action.payload.id
            ? { ...layout, ...action.payload.updates }
            : layout
        ),
      };

    case 'DELETE_LAYOUT':
      const filteredLayouts = state.layouts.filter(layout => layout.id !== action.payload);
      return {
        ...state,
        layouts: filteredLayouts,
        activeLayoutId: state.activeLayoutId === action.payload 
          ? (filteredLayouts[0]?.id || '') 
          : state.activeLayoutId,
      };

    case 'SET_ACTIVE_LAYOUT':
      return {
        ...state,
        activeLayoutId: action.payload,
      };

    case 'ADD_PANEL':
      return {
        ...state,
        layouts: state.layouts.map(layout =>
          layout.id === action.payload.layoutId
            ? { ...layout, panels: [...layout.panels, action.payload.panel] }
            : layout
        ),
      };

    case 'REMOVE_PANEL':
      return {
        ...state,
        layouts: state.layouts.map(layout =>
          layout.id === action.payload.layoutId
            ? { ...layout, panels: layout.panels.filter(panel => panel.id !== action.payload.panelId) }
            : layout
        ),
      };

    case 'UPDATE_PANEL':
      return {
        ...state,
        layouts: state.layouts.map(layout =>
          layout.id === action.payload.layoutId
            ? {
                ...layout,
                panels: layout.panels.map(panel =>
                  panel.id === action.payload.panelId
                    ? { ...panel, ...action.payload.updates }
                    : panel
                ),
              }
            : layout
        ),
      };

    case 'ADD_TAB':
      return {
        ...state,
        layouts: state.layouts.map(layout =>
          layout.id === action.payload.layoutId
            ? {
                ...layout,
                tabGroups: layout.tabGroups.map(group =>
                  group.id === action.payload.groupId
                    ? {
                        ...group,
                        tabs: action.payload.index !== undefined
                          ? [
                              ...group.tabs.slice(0, action.payload.index),
                              action.payload.tab,
                              ...group.tabs.slice(action.payload.index),
                            ]
                          : [...group.tabs, action.payload.tab],
                        activeTabId: action.payload.tab.id,
                      }
                    : group
                ),
              }
            : layout
        ),
      };

    case 'REMOVE_TAB':
      return {
        ...state,
        layouts: state.layouts.map(layout =>
          layout.id === action.payload.layoutId
            ? {
                ...layout,
                tabGroups: layout.tabGroups.map(group =>
                  group.id === action.payload.groupId
                    ? {
                        ...group,
                        tabs: group.tabs.filter(tab => tab.id !== action.payload.tabId),
                        activeTabId: group.activeTabId === action.payload.tabId
                          ? group.tabs.find(tab => tab.id !== action.payload.tabId)?.id || ''
                          : group.activeTabId,
                      }
                    : group
                ),
              }
            : layout
        ),
      };

    case 'UPDATE_TAB':
      return {
        ...state,
        layouts: state.layouts.map(layout =>
          layout.id === action.payload.layoutId
            ? {
                ...layout,
                tabGroups: layout.tabGroups.map(group =>
                  group.id === action.payload.groupId
                    ? {
                        ...group,
                        tabs: group.tabs.map(tab =>
                          tab.id === action.payload.tabId
                            ? { ...tab, ...action.payload.updates }
                            : tab
                        ),
                      }
                    : group
                ),
              }
            : layout
        ),
      };

    case 'SET_ACTIVE_TAB':
      return {
        ...state,
        layouts: state.layouts.map(layout =>
          layout.id === action.payload.layoutId
            ? {
                ...layout,
                tabGroups: layout.tabGroups.map(group =>
                  group.id === action.payload.groupId
                    ? { ...group, activeTabId: action.payload.tabId }
                    : group
                ),
              }
            : layout
        ),
      };

    case 'UPDATE_PREFERENCES':
      return {
        ...state,
        preferences: { ...state.preferences, ...action.payload },
      };

    case 'LOAD_STATE':
      return action.payload;

    default:
      return state;
  }
}

// Context
const WorkspaceContext = createContext<WorkspaceContextType | null>(null);

// Provider component
export function WorkspaceProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(workspaceReducer, initialState);

  // Load state from localStorage on mount
  useEffect(() => {
    const savedState = localStorage.getItem('omnispace-workspace-state');
    if (savedState) {
      try {
        const parsedState = JSON.parse(savedState);
        dispatch({ type: 'LOAD_STATE', payload: parsedState });
      } catch (error) {
        console.error('Failed to load workspace state:', error);
      }
    }
  }, []);

  // Save state to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('omnispace-workspace-state', JSON.stringify(state));
  }, [state]);

  const activeLayout = state.layouts.find(layout => layout.id === state.activeLayoutId) || null;

  const createLayout = useCallback((config: Omit<WorkspaceLayoutConfig, 'id'>) => {
    const id = `layout-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    dispatch({ type: 'CREATE_LAYOUT', payload: { ...config, id } });
  }, []);

  const updateLayout = useCallback((id: string, updates: Partial<WorkspaceLayoutConfig>) => {
    dispatch({ type: 'UPDATE_LAYOUT', payload: { id, updates } });
  }, []);

  const deleteLayout = useCallback((id: string) => {
    dispatch({ type: 'DELETE_LAYOUT', payload: id });
  }, []);

  const setActiveLayout = useCallback((id: string) => {
    dispatch({ type: 'SET_ACTIVE_LAYOUT', payload: id });
  }, []);

  const addPanel = useCallback((layoutId: string, panel: PanelConfig, position?: string) => {
    dispatch({ type: 'ADD_PANEL', payload: { layoutId, panel, position } });
  }, []);

  const removePanel = useCallback((layoutId: string, panelId: string) => {
    dispatch({ type: 'REMOVE_PANEL', payload: { layoutId, panelId } });
  }, []);

  const updatePanel = useCallback((layoutId: string, panelId: string, updates: Partial<PanelConfig>) => {
    dispatch({ type: 'UPDATE_PANEL', payload: { layoutId, panelId, updates } });
  }, []);

  const addTab = useCallback((layoutId: string, groupId: string, tab: TabConfig, index?: number) => {
    dispatch({ type: 'ADD_TAB', payload: { layoutId, groupId, tab, index } });
  }, []);

  const removeTab = useCallback((layoutId: string, groupId: string, tabId: string) => {
    dispatch({ type: 'REMOVE_TAB', payload: { layoutId, groupId, tabId } });
  }, []);

  const updateTab = useCallback((layoutId: string, groupId: string, tabId: string, updates: Partial<TabConfig>) => {
    dispatch({ type: 'UPDATE_TAB', payload: { layoutId, groupId, tabId, updates } });
  }, []);

  const setActiveTab = useCallback((layoutId: string, groupId: string, tabId: string) => {
    dispatch({ type: 'SET_ACTIVE_TAB', payload: { layoutId, groupId, tabId } });
  }, []);

  const moveTab = useCallback((layoutId: string, fromGroupId: string, toGroupId: string, tabId: string, index?: number) => {
    dispatch({ type: 'MOVE_TAB', payload: { layoutId, fromGroupId, toGroupId, tabId, index } });
  }, []);

  const addTabGroup = useCallback((layoutId: string, group: TabGroupConfig, position?: string) => {
    dispatch({ type: 'ADD_TAB_GROUP', payload: { layoutId, group, position } });
  }, []);

  const removeTabGroup = useCallback((layoutId: string, groupId: string) => {
    dispatch({ type: 'REMOVE_TAB_GROUP', payload: { layoutId, groupId } });
  }, []);

  const splitTabGroup = useCallback((layoutId: string, groupId: string, direction: 'horizontal' | 'vertical') => {
    dispatch({ type: 'SPLIT_TAB_GROUP', payload: { layoutId, groupId, direction } });
  }, []);

  const updatePreferences = useCallback((updates: Partial<WorkspacePreferences>) => {
    dispatch({ type: 'UPDATE_PREFERENCES', payload: updates });
  }, []);

  const saveLayout = useCallback((layoutId: string) => {
    // Implementation for saving layout to external storage
    console.log('Saving layout:', layoutId);
  }, []);

  const loadLayout = useCallback((layoutId: string) => {
    // Implementation for loading layout from external storage
    console.log('Loading layout:', layoutId);
  }, []);

  const exportLayout = useCallback((layoutId: string) => {
    const layout = state.layouts.find(l => l.id === layoutId);
    return layout ? JSON.stringify(layout, null, 2) : '';
  }, [state.layouts]);

  const importLayout = useCallback((data: string) => {
    try {
      const layout = JSON.parse(data);
      createLayout(layout);
    } catch (error) {
      console.error('Failed to import layout:', error);
    }
  }, [createLayout]);

  const contextValue: WorkspaceContextType = {
    state,
    activeLayout,
    createLayout,
    updateLayout,
    deleteLayout,
    setActiveLayout,
    addPanel,
    removePanel,
    updatePanel,
    addTab,
    removeTab,
    updateTab,
    setActiveTab,
    moveTab,
    addTabGroup,
    removeTabGroup,
    splitTabGroup,
    updatePreferences,
    saveLayout,
    loadLayout,
    exportLayout,
    importLayout,
  };

  return (
    <WorkspaceContext.Provider value={contextValue}>
      {children}
    </WorkspaceContext.Provider>
  );
}

// Hook to use workspace context
export function useWorkspace() {
  const context = useContext(WorkspaceContext);
  if (!context) {
    throw new Error('useWorkspace must be used within a WorkspaceProvider');
  }
  return context;
}
