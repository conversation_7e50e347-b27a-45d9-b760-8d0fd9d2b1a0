'use client';

import React, { useMemo, useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Sparkles, 
  Code, 
  Eye, 
  EyeOff, 
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { ToolCallRenderer } from './tool-call-renderer';
import { DataPartRenderer } from './data-part-renderer';

// Workspace-specific tool components for Omnispace
import { WorkspaceTerminalTool } from './workspace-tools/terminal-tool';

interface GenerativeUIProps {
  toolCalls: any[];
  dataParts: any[];
  enablePreview?: boolean;
  showSource?: boolean;
  onToolUpdate?: (toolId: string, data: any) => void;
  onError?: (error: Error) => void;
  className?: string;
}

interface ToolComponentMapping {
  [toolName: string]: React.ComponentType<any>;
}

// Default tool component mappings
const defaultToolComponents: ToolComponentMapping = {
  // Basic tools
  weather: ToolCallRenderer,
  calculator: ToolCallRenderer,
  convertFahrenheitToCelsius: ToolCallRenderer,
  
  // Workspace-specific tools for Omnispace
  terminal: WorkspaceTerminalTool,
  
  // Development tools
  code_execution: ({ toolCall, ...props }) => (
    <CodeExecutionTool toolCall={toolCall} {...props} />
  ),
  file_system: ({ toolCall, ...props }) => (
    <FileSystemTool toolCall={toolCall} {...props} />
  ),
  browser_automation: ({ toolCall, ...props }) => (
    <BrowserAutomationTool toolCall={toolCall} {...props} />
  ),
};

export function GenerativeUI({
  toolCalls = [],
  dataParts = [],
  enablePreview = true,
  showSource = false,
  onToolUpdate,
  onError,
  className = '',
}: GenerativeUIProps) {
  const [previewMode, setPreviewMode] = useState(enablePreview);
  const [sourceVisible, setSourceVisible] = useState(showSource);
  const [toolComponents, setToolComponents] = useState<ToolComponentMapping>(defaultToolComponents);

  // Register custom tool component
  const registerToolComponent = useCallback((toolName: string, component: React.ComponentType<any>) => {
    setToolComponents(prev => ({
      ...prev,
      [toolName]: component,
    }));
  }, []);

  // Render individual tool call
  const renderToolCall = useCallback((toolCall: any, index: number) => {
    const toolName = toolCall.type.replace('tool-', '');
    const ToolComponent = toolComponents[toolName];

    if (!ToolComponent) {
      return (
        <Alert key={`tool-${index}`} variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Unknown tool: {toolName}. No component registered for this tool type.
          </AlertDescription>
        </Alert>
      );
    }

    try {
      return (
        <div key={`tool-${index}`} className="tool-call-container">
          <ToolComponent
            toolCall={toolCall}
            onUpdate={(data: any) => onToolUpdate?.(toolCall.id || `tool-${index}`, data)}
            onError={onError}
          />
        </div>
      );
    } catch (error) {
      console.error(`Error rendering tool ${toolName}:`, error);
      onError?.(error instanceof Error ? error : new Error(`Failed to render tool ${toolName}`));
      
      return (
        <Alert key={`tool-error-${index}`} variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to render {toolName} tool: {error instanceof Error ? error.message : 'Unknown error'}
          </AlertDescription>
        </Alert>
      );
    }
  }, [toolComponents, onToolUpdate, onError]);

  // Render data parts
  const renderDataPart = useCallback((dataPart: any, index: number) => {
    try {
      return (
        <div key={`data-${index}`} className="data-part-container">
          <DataPartRenderer dataPart={dataPart} />
        </div>
      );
    } catch (error) {
      console.error(`Error rendering data part:`, error);
      onError?.(error instanceof Error ? error : new Error('Failed to render data part'));
      
      return (
        <Alert key={`data-error-${index}`} variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to render data part: {error instanceof Error ? error.message : 'Unknown error'}
          </AlertDescription>
        </Alert>
      );
    }
  }, [onError]);

  // Generate UI components from tool calls and data parts
  const generatedComponents = useMemo(() => {
    const components: React.ReactNode[] = [];

    // Render tool calls
    toolCalls.forEach((toolCall, index) => {
      components.push(renderToolCall(toolCall, index));
    });

    // Render data parts
    dataParts.forEach((dataPart, index) => {
      components.push(renderDataPart(dataPart, index));
    });

    return components;
  }, [toolCalls, dataParts, renderToolCall, renderDataPart]);

  const hasContent = toolCalls.length > 0 || dataParts.length > 0;

  if (!hasContent) {
    return null;
  }

  return (
    <Card className={`generative-ui-container ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-purple-500" />
            <CardTitle className="text-lg">Generated UI</CardTitle>
            <Badge variant="outline">
              {toolCalls.length} tools, {dataParts.length} data
            </Badge>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setPreviewMode(!previewMode)}
            >
              {previewMode ? (
                <>
                  <EyeOff className="h-4 w-4 mr-1" />
                  Hide Preview
                </>
              ) : (
                <>
                  <Eye className="h-4 w-4 mr-1" />
                  Show Preview
                </>
              )}
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSourceVisible(!sourceVisible)}
            >
              <Code className="h-4 w-4 mr-1" />
              {sourceVisible ? 'Hide' : 'Show'} Source
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {previewMode && (
          <div className="generative-ui-preview space-y-4">
            {generatedComponents}
          </div>
        )}

        {sourceVisible && (
          <div className="generative-ui-source">
            <h4 className="text-sm font-medium mb-2">Source Data</h4>
            <div className="space-y-2">
              {toolCalls.length > 0 && (
                <div>
                  <h5 className="text-xs font-medium text-muted-foreground mb-1">Tool Calls</h5>
                  <pre className="text-xs bg-muted p-3 rounded-md overflow-x-auto">
                    {JSON.stringify(toolCalls, null, 2)}
                  </pre>
                </div>
              )}
              
              {dataParts.length > 0 && (
                <div>
                  <h5 className="text-xs font-medium text-muted-foreground mb-1">Data Parts</h5>
                  <pre className="text-xs bg-muted p-3 rounded-md overflow-x-auto">
                    {JSON.stringify(dataParts, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Specialized tool components for different use cases

// Code execution tool component
function CodeExecutionTool({ toolCall, onUpdate, onError }: any) {
  const [isExecuting, setIsExecuting] = useState(false);
  const [output, setOutput] = useState('');

  const executeCode = useCallback(async () => {
    setIsExecuting(true);
    try {
      // Simulate code execution (in real implementation, this would call an API)
      await new Promise(resolve => setTimeout(resolve, 2000));
      const mockOutput = `Executed: ${toolCall.args?.code}\nResult: Success`;
      setOutput(mockOutput);
      onUpdate?.({ output: mockOutput, status: 'completed' });
    } catch (error) {
      onError?.(error);
    } finally {
      setIsExecuting(false);
    }
  }, [toolCall.args?.code, onUpdate, onError]);

  return (
    <Card className="border-l-4 border-l-blue-500">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Code className="h-4 w-4 text-blue-500" />
            <CardTitle className="text-sm">Code Execution</CardTitle>
            <Badge variant="outline">
              {toolCall.args?.language || 'unknown'}
            </Badge>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={executeCode}
            disabled={isExecuting}
          >
            {isExecuting ? (
              <Loader2 className="h-4 w-4 animate-spin mr-1" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-1" />
            )}
            Execute
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div>
          <h4 className="text-sm font-medium mb-2">Code</h4>
          <pre className="text-xs bg-muted p-3 rounded-md overflow-x-auto">
            <code>{toolCall.args?.code}</code>
          </pre>
        </div>
        
        {output && (
          <div>
            <h4 className="text-sm font-medium mb-2">Output</h4>
            <pre className="text-xs bg-green-50 dark:bg-green-950/20 p-3 rounded-md overflow-x-auto">
              {output}
            </pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// File system tool component
function FileSystemTool({ toolCall, onUpdate, onError }: any) {
  const operation = toolCall.args?.operation;
  const path = toolCall.args?.path;
  const content = toolCall.args?.content;

  return (
    <Card className="border-l-4 border-l-green-500">
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <Code className="h-4 w-4 text-green-500" />
          <CardTitle className="text-sm">File System Operation</CardTitle>
          <Badge variant="outline" className="capitalize">
            {operation}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div>
          <h4 className="text-sm font-medium mb-1">Path</h4>
          <code className="text-xs bg-muted px-2 py-1 rounded">{path}</code>
        </div>
        
        {content && (
          <div>
            <h4 className="text-sm font-medium mb-2">Content</h4>
            <pre className="text-xs bg-muted p-3 rounded-md overflow-x-auto max-h-32">
              {content}
            </pre>
          </div>
        )}
        
        {toolCall.result && (
          <div className="flex items-center gap-2 text-sm text-green-600">
            <CheckCircle className="h-4 w-4" />
            <span>Operation completed successfully</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Browser automation tool component
function BrowserAutomationTool({ toolCall, onUpdate, onError }: any) {
  const action = toolCall.args?.action;
  const url = toolCall.args?.url;
  const selector = toolCall.args?.selector;

  return (
    <Card className="border-l-4 border-l-purple-500">
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <Code className="h-4 w-4 text-purple-500" />
          <CardTitle className="text-sm">Browser Automation</CardTitle>
          <Badge variant="outline" className="capitalize">
            {action}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {url && (
          <div>
            <h4 className="text-sm font-medium mb-1">URL</h4>
            <code className="text-xs bg-muted px-2 py-1 rounded">{url}</code>
          </div>
        )}
        
        {selector && (
          <div>
            <h4 className="text-sm font-medium mb-1">Selector</h4>
            <code className="text-xs bg-muted px-2 py-1 rounded">{selector}</code>
          </div>
        )}
        
        {toolCall.result && (
          <div>
            <h4 className="text-sm font-medium mb-2">Result</h4>
            <pre className="text-xs bg-muted p-3 rounded-md overflow-x-auto">
              {JSON.stringify(toolCall.result, null, 2)}
            </pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Export the register function for external use
export { defaultToolComponents };
export type { ToolComponentMapping };
