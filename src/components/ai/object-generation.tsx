'use client';

import React, { useState, useCallback } from 'react';
import { useObject } from '@ai-sdk/react';
import { z } from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { 
  Sparkles, 
  User, 
  ChefHat, 
  FileText, 
  Code, 
  Database,
  Copy,
  Download,
  RefreshCw,
  Square,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { ObjectGenerationLoadingIndicator } from './loading-indicator';
import { ErrorDisplay } from './error-display';

// Schema definitions
const PersonSchema = z.object({
  name: z.string().describe('Full name of the person'),
  age: z.number().min(0).max(150).describe('Age in years'),
  occupation: z.string().describe('Job or profession'),
  location: z.object({
    city: z.string().describe('City name'),
    country: z.string().describe('Country name'),
  }).describe('Location information'),
  hobbies: z.array(z.string()).describe('List of hobbies and interests'),
  personality: z.object({
    traits: z.array(z.string()).describe('Personality traits'),
    description: z.string().describe('Brief personality description'),
  }).describe('Personality information'),
  background: z.string().describe('Brief background story'),
});

const RecipeSchema = z.object({
  name: z.string().describe('Recipe name'),
  description: z.string().describe('Brief description'),
  cuisine: z.string().describe('Cuisine type'),
  difficulty: z.enum(['easy', 'medium', 'hard']).describe('Difficulty level'),
  servings: z.number().min(1).max(20).describe('Number of servings'),
  prepTime: z.string().describe('Preparation time'),
  cookTime: z.string().describe('Cooking time'),
  ingredients: z.array(z.object({
    item: z.string().describe('Ingredient name'),
    amount: z.string().describe('Amount needed'),
    notes: z.string().optional().describe('Optional notes'),
  })).describe('List of ingredients'),
  instructions: z.array(z.string()).describe('Step-by-step instructions'),
  tags: z.array(z.string()).describe('Recipe tags'),
  nutrition: z.object({
    calories: z.number().optional(),
    protein: z.string().optional(),
    carbs: z.string().optional(),
    fat: z.string().optional(),
  }).optional().describe('Nutritional information'),
});

const ArticleSchema = z.object({
  title: z.string().describe('Article title'),
  subtitle: z.string().optional().describe('Article subtitle'),
  author: z.string().describe('Author name'),
  category: z.string().describe('Article category'),
  tags: z.array(z.string()).describe('Article tags'),
  summary: z.string().describe('Brief summary'),
  content: z.array(z.object({
    type: z.enum(['paragraph', 'heading', 'list', 'quote']),
    content: z.string(),
    level: z.number().optional().describe('Heading level for headings'),
  })).describe('Article content structure'),
  wordCount: z.number().describe('Estimated word count'),
  readingTime: z.string().describe('Estimated reading time'),
});

const schemas = {
  person: PersonSchema,
  recipe: RecipeSchema,
  article: ArticleSchema,
};

type SchemaType = keyof typeof schemas;

interface ObjectGenerationProps {
  api?: string;
  defaultType?: SchemaType;
  showTypeSelector?: boolean;
  showProgress?: boolean;
  allowExport?: boolean;
  className?: string;
  onGenerated?: (object: any, type: SchemaType) => void;
}

export function ObjectGeneration({
  api = '/api/generate-object',
  defaultType = 'person',
  showTypeSelector = true,
  showProgress = true,
  allowExport = true,
  className = '',
  onGenerated,
}: ObjectGenerationProps) {
  const [selectedType, setSelectedType] = useState<SchemaType>(defaultType);
  const [prompt, setPrompt] = useState('');
  const [generationProgress, setGenerationProgress] = useState(0);

  const {
    object,
    submit,
    stop,
    isLoading,
    error,
  } = useObject({
    api,
    schema: schemas[selectedType],
    onFinish: (result) => {
      if (result.object) {
        onGenerated?.(result.object, selectedType);
        setGenerationProgress(100);
      }
    },
  });

  // Simulate progress for better UX
  React.useEffect(() => {
    if (isLoading) {
      setGenerationProgress(0);
      const interval = setInterval(() => {
        setGenerationProgress(prev => {
          if (prev >= 90) return prev;
          return prev + Math.random() * 10;
        });
      }, 500);
      return () => clearInterval(interval);
    }
  }, [isLoading]);

  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    if (!prompt.trim() || isLoading) return;
    
    setGenerationProgress(0);
    submit(prompt);
  }, [prompt, isLoading, submit]);

  const handleExport = useCallback(() => {
    if (!object) return;
    
    const exportData = {
      type: selectedType,
      prompt,
      object,
      generatedAt: new Date().toISOString(),
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${selectedType}-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [object, selectedType, prompt]);

  const handleCopy = useCallback(() => {
    if (!object) return;
    navigator.clipboard.writeText(JSON.stringify(object, null, 2));
  }, [object]);

  const getTypeIcon = (type: SchemaType) => {
    switch (type) {
      case 'person':
        return <User className="h-4 w-4" />;
      case 'recipe':
        return <ChefHat className="h-4 w-4" />;
      case 'article':
        return <FileText className="h-4 w-4" />;
      default:
        return <Database className="h-4 w-4" />;
    }
  };

  const getPlaceholder = (type: SchemaType) => {
    switch (type) {
      case 'person':
        return 'Describe a person (e.g., "A software engineer from Tokyo who loves hiking and photography")';
      case 'recipe':
        return 'Describe a recipe (e.g., "A healthy vegetarian pasta dish with Mediterranean flavors")';
      case 'article':
        return 'Describe an article (e.g., "An article about the future of artificial intelligence in healthcare")';
      default:
        return 'Describe what you want to generate...';
    }
  };

  const renderObjectPreview = () => {
    if (!object) return null;

    switch (selectedType) {
      case 'person':
        return <PersonPreview person={object} />;
      case 'recipe':
        return <RecipePreview recipe={object} />;
      case 'article':
        return <ArticlePreview article={object} />;
      default:
        return <GenericPreview object={object} />;
    }
  };

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-yellow-500" />
            <CardTitle>Object Generation</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            {object && (
              <>
                {allowExport && (
                  <Button variant="outline" size="sm" onClick={handleExport}>
                    <Download className="h-4 w-4 mr-1" />
                    Export
                  </Button>
                )}
                <Button variant="outline" size="sm" onClick={handleCopy}>
                  <Copy className="h-4 w-4 mr-1" />
                  Copy
                </Button>
              </>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {showTypeSelector && (
          <div className="space-y-2">
            <label className="text-sm font-medium">Object Type</label>
            <Select
              value={selectedType}
              onValueChange={(value: SchemaType) => setSelectedType(value)}
              disabled={isLoading}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="person">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Person Profile
                  </div>
                </SelectItem>
                <SelectItem value="recipe">
                  <div className="flex items-center gap-2">
                    <ChefHat className="h-4 w-4" />
                    Recipe
                  </div>
                </SelectItem>
                <SelectItem value="article">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Article
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Description</label>
            <Textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder={getPlaceholder(selectedType)}
              disabled={isLoading}
              rows={3}
              className="resize-none"
            />
          </div>

          <div className="flex gap-2">
            {isLoading ? (
              <Button type="button" variant="outline" onClick={stop}>
                <Square className="h-4 w-4 mr-2" />
                Stop
              </Button>
            ) : (
              <Button type="submit" disabled={!prompt.trim()}>
                {getTypeIcon(selectedType)}
                <span className="ml-2">Generate {selectedType}</span>
              </Button>
            )}
          </div>
        </form>

        {isLoading && (
          <div className="space-y-3">
            <ObjectGenerationLoadingIndicator schema={selectedType} />
            {showProgress && (
              <div className="space-y-2">
                <Progress value={generationProgress} className="h-2" />
                <div className="text-xs text-muted-foreground text-center">
                  {generationProgress.toFixed(0)}% complete
                </div>
              </div>
            )}
          </div>
        )}

        {error && (
          <ErrorDisplay
            error={error}
            onRetry={() => submit(prompt)}
            showDetails
          />
        )}

        {object && (
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <h3 className="text-lg font-semibold">Generated {selectedType}</h3>
              <Badge variant="outline" className="capitalize">
                {selectedType}
              </Badge>
            </div>
            {renderObjectPreview()}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Preview components for different object types
function PersonPreview({ person }: { person: any }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          {person.name}
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          {person.age} years old • {person.occupation}
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h4 className="font-medium mb-2">Location</h4>
          <p className="text-sm">{person.location.city}, {person.location.country}</p>
        </div>
        
        <div>
          <h4 className="font-medium mb-2">Background</h4>
          <p className="text-sm">{person.background}</p>
        </div>
        
        <div>
          <h4 className="font-medium mb-2">Personality</h4>
          <p className="text-sm mb-2">{person.personality.description}</p>
          <div className="flex flex-wrap gap-1">
            {person.personality.traits.map((trait: string, index: number) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {trait}
              </Badge>
            ))}
          </div>
        </div>
        
        <div>
          <h4 className="font-medium mb-2">Hobbies</h4>
          <div className="flex flex-wrap gap-1">
            {person.hobbies.map((hobby: string, index: number) => (
              <Badge key={index} variant="outline" className="text-xs">
                {hobby}
              </Badge>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function RecipePreview({ recipe }: { recipe: any }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ChefHat className="h-5 w-5" />
          {recipe.name}
        </CardTitle>
        <p className="text-sm text-muted-foreground">{recipe.description}</p>
        <div className="flex gap-2 mt-2">
          <Badge variant="outline">{recipe.cuisine}</Badge>
          <Badge variant="outline" className="capitalize">{recipe.difficulty}</Badge>
          <Badge variant="outline">{recipe.servings} servings</Badge>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="ingredients" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="ingredients">Ingredients</TabsTrigger>
            <TabsTrigger value="instructions">Instructions</TabsTrigger>
            <TabsTrigger value="info">Info</TabsTrigger>
          </TabsList>
          
          <TabsContent value="ingredients" className="space-y-2">
            {recipe.ingredients.map((ingredient: any, index: number) => (
              <div key={index} className="flex justify-between items-center p-2 bg-muted rounded">
                <span className="text-sm">{ingredient.item}</span>
                <span className="text-sm font-medium">{ingredient.amount}</span>
              </div>
            ))}
          </TabsContent>
          
          <TabsContent value="instructions" className="space-y-2">
            {recipe.instructions.map((instruction: string, index: number) => (
              <div key={index} className="flex gap-3 p-2">
                <Badge variant="outline" className="text-xs min-w-[24px] h-6">
                  {index + 1}
                </Badge>
                <p className="text-sm">{instruction}</p>
              </div>
            ))}
          </TabsContent>
          
          <TabsContent value="info" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-1">Prep Time</h4>
                <p className="text-sm text-muted-foreground">{recipe.prepTime}</p>
              </div>
              <div>
                <h4 className="font-medium mb-1">Cook Time</h4>
                <p className="text-sm text-muted-foreground">{recipe.cookTime}</p>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Tags</h4>
              <div className="flex flex-wrap gap-1">
                {recipe.tags.map((tag: string, index: number) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
            
            {recipe.nutrition && (
              <div>
                <h4 className="font-medium mb-2">Nutrition (per serving)</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  {recipe.nutrition.calories && (
                    <div>Calories: {recipe.nutrition.calories}</div>
                  )}
                  {recipe.nutrition.protein && (
                    <div>Protein: {recipe.nutrition.protein}</div>
                  )}
                  {recipe.nutrition.carbs && (
                    <div>Carbs: {recipe.nutrition.carbs}</div>
                  )}
                  {recipe.nutrition.fat && (
                    <div>Fat: {recipe.nutrition.fat}</div>
                  )}
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

function ArticlePreview({ article }: { article: any }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          {article.title}
        </CardTitle>
        {article.subtitle && (
          <p className="text-lg text-muted-foreground">{article.subtitle}</p>
        )}
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <span>By {article.author}</span>
          <Badge variant="outline">{article.category}</Badge>
          <span>{article.readingTime}</span>
          <span>{article.wordCount} words</span>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h4 className="font-medium mb-2">Summary</h4>
          <p className="text-sm text-muted-foreground">{article.summary}</p>
        </div>
        
        <div>
          <h4 className="font-medium mb-2">Content Structure</h4>
          <div className="space-y-2">
            {article.content.slice(0, 5).map((section: any, index: number) => (
              <div key={index} className="p-2 bg-muted rounded text-sm">
                <Badge variant="outline" className="text-xs mr-2 capitalize">
                  {section.type}
                </Badge>
                <span className="text-muted-foreground">
                  {section.content.substring(0, 100)}...
                </span>
              </div>
            ))}
            {article.content.length > 5 && (
              <p className="text-xs text-muted-foreground">
                +{article.content.length - 5} more sections
              </p>
            )}
          </div>
        </div>
        
        <div>
          <h4 className="font-medium mb-2">Tags</h4>
          <div className="flex flex-wrap gap-1">
            {article.tags.map((tag: string, index: number) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function GenericPreview({ object }: { object: any }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Generated Object
        </CardTitle>
      </CardHeader>
      <CardContent>
        <pre className="text-xs bg-muted p-4 rounded-md overflow-x-auto">
          {JSON.stringify(object, null, 2)}
        </pre>
      </CardContent>
    </Card>
  );
}
